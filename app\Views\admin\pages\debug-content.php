<?php /** @var string $title */ /** @var string|null $sidebar */ /** @var string|null $header */ ?>
<?= $this->extend('layouts/admin') ?>

<?= $this->section('head') ?>
<!-- Debug content page specific head content -->
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="mx-auto max-w-(--breakpoint-2xl) p-4 md:p-6">
    <!-- Breadcrumb -->
    <div class="mb-6 flex flex-wrap items-center justify-between gap-3">
        <div>
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="/admin" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">Debug Content</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Debug Information -->
    <div class="grid grid-cols-12 gap-4 md:gap-6">
        <div class="col-span-12">
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
                <h3 class="mb-6 text-title-md font-bold text-gray-800 dark:text-white/90">Content Extraction Debug</h3>
                
                <!-- Alpine.js Status -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">Alpine.js Status</h4>
                    <div id="alpine-status" class="p-4 bg-yellow-100 border border-yellow-300 rounded dark:bg-yellow-900 dark:border-yellow-700">
                        <p class="text-yellow-800 dark:text-yellow-200">⏳ Checking Alpine.js...</p>
                    </div>
                </div>

                <!-- Simple Alpine.js Test -->
                <div class="mb-8" x-data="{ test: 'Alpine.js is working!' }">
                    <h4 class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">Simple Alpine.js Test</h4>
                    <div class="p-4 bg-green-100 border border-green-300 rounded dark:bg-green-900 dark:border-green-700">
                        <p class="text-green-800 dark:text-green-200" x-text="test"></p>
                    </div>
                </div>

                <!-- Variables Debug -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">Variables Debug</h4>
                    <div class="space-y-4">
                        <div class="p-4 bg-gray-100 rounded dark:bg-gray-700">
                            <h5 class="font-semibold text-gray-800 dark:text-white mb-2">Sidebar Variable:</h5>
                            <pre class="text-sm text-gray-600 dark:text-gray-300 overflow-x-auto">
<?php if (isset($sidebar)): ?>
Sidebar is set: <?= $sidebar ? 'Yes (length: ' . strlen($sidebar) . ')' : 'No (empty)' ?>
<?php else: ?>
Sidebar variable not set
<?php endif; ?>
                            </pre>
                        </div>
                        
                        <div class="p-4 bg-gray-100 rounded dark:bg-gray-700">
                            <h5 class="font-semibold text-gray-800 dark:text-white mb-2">Header Variable:</h5>
                            <pre class="text-sm text-gray-600 dark:text-gray-300 overflow-x-auto">
<?php if (isset($header)): ?>
Header is set: <?= $header ? 'Yes (length: ' . strlen($header) . ')' : 'No (empty)' ?>
<?php else: ?>
Header variable not set
<?php endif; ?>
                            </pre>
                        </div>
                    </div>
                </div>

                <!-- Layout Test -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">Layout Test</h4>
                    <div class="p-4 bg-blue-100 border border-blue-300 rounded dark:bg-blue-900 dark:border-blue-700">
                        <p class="text-blue-800 dark:text-blue-200">
                            This content is rendered through the admin layout. If you can see this properly formatted, 
                            the layout system is working correctly.
                        </p>
                    </div>
                </div>

                <!-- Dark Mode Test -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">Dark Mode Test</h4>
                    <div class="p-4 bg-purple-100 border border-purple-300 rounded dark:bg-purple-900 dark:border-purple-700">
                        <p class="text-purple-800 dark:text-purple-200">
                            This text should change color based on dark mode. 
                            <button 
                                @click="darkMode = !darkMode"
                                class="ml-2 px-3 py-1 bg-purple-600 text-white rounded hover:bg-purple-700"
                            >
                                Toggle Dark Mode
                            </button>
                        </p>
                    </div>
                </div>

                <!-- Console Output -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">Console Output</h4>
                    <div id="console-output" class="p-4 bg-black text-green-400 rounded font-mono text-sm h-32 overflow-y-auto">
                        > Debug page loaded<br>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function logToConsole(message) {
    const consoleOutput = document.getElementById('console-output');
    const timestamp = new Date().toLocaleTimeString();
    consoleOutput.innerHTML += `> [${timestamp}] ${message}<br>`;
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
}

document.addEventListener('DOMContentLoaded', function() {
    const statusDiv = document.getElementById('alpine-status');
    
    logToConsole('DOM loaded');
    
    // Check Alpine.js status
    setTimeout(function() {
        if (typeof Alpine !== 'undefined') {
            statusDiv.innerHTML = '<p class="text-green-800 dark:text-green-200">✅ Alpine.js is loaded and working!</p>';
            statusDiv.className = 'p-4 bg-green-100 border border-green-300 rounded dark:bg-green-900 dark:border-green-700';
            logToConsole('Alpine.js loaded successfully');
        } else {
            statusDiv.innerHTML = '<p class="text-red-800 dark:text-red-200">❌ Alpine.js is not loaded</p>';
            statusDiv.className = 'p-4 bg-red-100 border border-red-300 rounded dark:bg-red-900 dark:border-red-700';
            logToConsole('Alpine.js not loaded');
        }
        
        // Check if we can access Alpine data
        try {
            const bodyData = document.body._x_dataStack;
            if (bodyData) {
                logToConsole('Alpine.js data stack found on body');
            } else {
                logToConsole('No Alpine.js data stack on body');
            }
        } catch (e) {
            logToConsole('Error checking Alpine.js data: ' + e.message);
        }
        
        // Check localStorage
        try {
            const darkMode = localStorage.getItem('darkMode');
            logToConsole('Dark mode setting: ' + (darkMode || 'not set'));
        } catch (e) {
            logToConsole('Error accessing localStorage: ' + e.message);
        }
        
    }, 1000);
});
</script>
<?= $this->endSection() ?>
