<?php /** @var string $title */ /** @var string|null $sidebar */ /** @var string|null $header */ ?>
<?= $this->extend('layouts/admin') ?>

<?= $this->section('head') ?>
<!-- Dashboard specific head content -->
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="p-4 mx-auto max-w-(--breakpoint-2xl) md:p-6">
    <div class="grid grid-cols-12 gap-4 md:gap-6">
        <div class="col-span-12 space-y-6 xl:col-span-7">
            <!-- Metrics Row -->
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:gap-6">
                <!-- Customers Metric -->
                <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
                    <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-gray-100 dark:bg-gray-800">
                        <svg class="fill-gray-800 dark:fill-white/90" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.80443 5.60156C7.59109 5.60156 6.60749 6.58517 6.60749 7.79851C6.60749 9.01185 7.59109 9.99545 8.80443 9.99545C10.0178 9.99545 11.0014 9.01185 11.0014 7.79851C11.0014 6.58517 10.0178 5.60156 8.80443 5.60156ZM5.10749 7.79851C5.10749 5.75674 6.76267 4.10156 8.80443 4.10156C10.8462 4.10156 12.5014 5.75674 12.5014 7.79851C12.5014 9.84027 10.8462 11.4955 8.80443 11.4955C6.76267 11.4955 5.10749 9.84027 5.10749 7.79851Z" fill=""/>
                        </svg>
                    </div>
                    <div class="mt-5 flex items-end justify-between">
                        <div>
                            <span class="text-sm text-gray-500 dark:text-gray-400">Customers</span>
                            <h4 class="mt-2 text-title-sm font-bold text-gray-800 dark:text-white/90">
                                <?= number_format($customers ?? 3782) ?>
                            </h4>
                        </div>
                        <span class="flex items-center gap-1 rounded-full bg-success-50 py-0.5 pl-2 pr-2.5 text-sm font-medium text-success-600 dark:bg-success-500/15 dark:text-success-500">
                            <svg class="fill-current" width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.56462 1.62393C5.70193 1.47072 5.90135 1.37432 6.12329 1.37432L9.65514 4.5918C9.94814 4.88459 9.94831 5.35947 9.65552 5.65246L6.87329 3.93247V10.125C6.87329 10.5392 6.53751 10.875 6.12329 10.875C5.70908 10.875 5.37329 10.5392 5.37329 10.125V3.93578L2.59484 4.59182Z" fill=""/>
                            </svg>
                            <?= $customerGrowth ?? '18%' ?>
                        </span>
                    </div>
                </div>

                <!-- Revenue Metric -->
                <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
                    <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-gray-100 dark:bg-gray-800">
                        <svg class="fill-gray-800 dark:fill-white/90" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <div class="mt-5 flex items-end justify-between">
                        <div>
                            <span class="text-sm text-gray-500 dark:text-gray-400">Revenue</span>
                            <h4 class="mt-2 text-title-sm font-bold text-gray-800 dark:text-white/90">
                                $<?= number_format($revenue ?? 45231, 2) ?>
                            </h4>
                        </div>
                        <span class="flex items-center gap-1 rounded-full bg-success-50 py-0.5 pl-2 pr-2.5 text-sm font-medium text-success-600 dark:bg-success-500/15 dark:text-success-500">
                            <svg class="fill-current" width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.56462 1.62393C5.70193 1.47072 5.90135 1.37432 6.12329 1.37432L9.65514 4.5918C9.94814 4.88459 9.94831 5.35947 9.65552 5.65246L6.87329 3.93247V10.125C6.87329 10.5392 6.53751 10.875 6.12329 10.875C5.70908 10.875 5.37329 10.5392 5.37329 10.125V3.93578L2.59484 4.59182Z" fill=""/>
                            </svg>
                            <?= $revenueGrowth ?? '12%' ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Chart Section -->
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
                <div class="mb-6 flex items-center justify-between">
                    <h3 class="text-title-md font-bold text-gray-800 dark:text-white/90">Sales Overview</h3>
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-gray-500 dark:text-gray-400">This Month</span>
                    </div>
                </div>
                <div id="salesChart" class="h-80">
                    <!-- Chart will be rendered here via JavaScript -->
                    <div class="flex h-full items-center justify-center text-gray-500 dark:text-gray-400">
                        Sales Chart Placeholder
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-span-12 space-y-6 xl:col-span-5">
            <!-- Recent Activity -->
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
                <h3 class="mb-6 text-title-md font-bold text-gray-800 dark:text-white/90">Recent Activity</h3>
                <div class="space-y-4">
                    <?php foreach ($recentActivity ?? [] as $activity): ?>
                    <div class="flex items-center gap-3">
                        <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-800 dark:text-white/90"><?= esc($activity['message']) ?></p>
                            <span class="text-xs text-gray-500 dark:text-gray-400"><?= esc($activity['time']) ?></span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    
                    <?php if (empty($recentActivity)): ?>
                    <div class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400">No recent activity</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Top Products -->
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
                <h3 class="mb-6 text-title-md font-bold text-gray-800 dark:text-white/90">Top Products</h3>
                <div class="space-y-4">
                    <?php foreach ($topProducts ?? [] as $product): ?>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <img src="<?= esc($product['image']) ?>" alt="<?= esc($product['name']) ?>" class="h-10 w-10 rounded-lg object-cover">
                            <div>
                                <p class="text-sm font-medium text-gray-800 dark:text-white/90"><?= esc($product['name']) ?></p>
                                <span class="text-xs text-gray-500 dark:text-gray-400"><?= esc($product['category']) ?></span>
                            </div>
                        </div>
                        <span class="text-sm font-medium text-gray-800 dark:text-white/90">$<?= number_format($product['price'], 2) ?></span>
                    </div>
                    <?php endforeach; ?>
                    
                    <?php if (empty($topProducts)): ?>
                    <div class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400">No products data</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Dashboard specific JavaScript
console.log('Dashboard loaded');
</script>
<?= $this->endSection() ?>
