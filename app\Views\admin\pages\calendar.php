<?php /** @var string $title */ /** @var string|null $sidebar */ /** @var string|null $header */ ?>
<?= $this->extend('layouts/admin') ?>

<?= $this->section('head') ?>
<!-- Calendar specific head content -->
<style>
.fc-event {
    border: none !important;
    background: transparent !important;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="mx-auto max-w-(--breakpoint-2xl) p-4 md:p-6">
    <!-- Breadcrumb -->
    <div class="mb-6 flex flex-wrap items-center justify-between gap-3">
        <div>
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="/admin" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">Calendar</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
        <button class="inline-flex items-center justify-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
            Add Event
        </button>
    </div>

    <!-- Calendar Container -->
    <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
        <div class="mb-6 flex items-center justify-between">
            <h3 class="text-title-md font-bold text-gray-800 dark:text-white/90">Calendar</h3>
            <div class="flex items-center gap-2">
                <button class="rounded-lg border border-gray-200 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                    Today
                </button>
                <div class="flex items-center">
                    <button class="rounded-l-lg border border-gray-200 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                        ‹
                    </button>
                    <button class="rounded-r-lg border-t border-r border-b border-gray-200 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                        ›
                    </button>
                </div>
            </div>
        </div>

        <!-- Calendar Grid -->
        <div id="calendar" class="min-h-[600px]">
            <!-- Calendar will be rendered here -->
            <div class="grid grid-cols-7 gap-1">
                <!-- Calendar Header -->
                <div class="p-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300">Sun</div>
                <div class="p-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300">Mon</div>
                <div class="p-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300">Tue</div>
                <div class="p-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300">Wed</div>
                <div class="p-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300">Thu</div>
                <div class="p-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300">Fri</div>
                <div class="p-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300">Sat</div>

                <!-- Calendar Days -->
                <?php 
                $currentDate = new DateTime();
                $firstDay = new DateTime($currentDate->format('Y-m-01'));
                $lastDay = new DateTime($currentDate->format('Y-m-t'));
                $startDate = clone $firstDay;
                $startDate->modify('last sunday');
                
                for ($i = 0; $i < 42; $i++): 
                    $date = clone $startDate;
                    $date->modify("+{$i} days");
                    $isCurrentMonth = $date->format('m') == $currentDate->format('m');
                    $isToday = $date->format('Y-m-d') == $currentDate->format('Y-m-d');
                ?>
                <div class="min-h-[80px] border border-gray-100 p-2 dark:border-gray-700 <?= $isCurrentMonth ? 'bg-white dark:bg-gray-800' : 'bg-gray-50 dark:bg-gray-900' ?>">
                    <div class="text-sm <?= $isToday ? 'font-bold text-blue-600' : ($isCurrentMonth ? 'text-gray-900 dark:text-white' : 'text-gray-400') ?>">
                        <?= $date->format('j') ?>
                    </div>
                    
                    <?php if (isset($events[$date->format('Y-m-d')])): ?>
                        <?php foreach ($events[$date->format('Y-m-d')] as $event): ?>
                        <div class="mt-1 rounded bg-blue-100 px-1 py-0.5 text-xs text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            <?= esc($event['title']) ?>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <?php endfor; ?>
            </div>
        </div>
    </div>

    <!-- Upcoming Events -->
    <div class="mt-6 rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
        <h3 class="mb-6 text-title-md font-bold text-gray-800 dark:text-white/90">Upcoming Events</h3>
        <div class="space-y-4">
            <?php foreach ($upcomingEvents ?? [] as $event): ?>
            <div class="flex items-center gap-4 rounded-lg border border-gray-100 p-4 dark:border-gray-700">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
                    <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900 dark:text-white"><?= esc($event['title']) ?></h4>
                    <p class="text-sm text-gray-500 dark:text-gray-400"><?= esc($event['description']) ?></p>
                    <span class="text-xs text-gray-400"><?= esc($event['date']) ?> at <?= esc($event['time']) ?></span>
                </div>
                <div class="flex items-center gap-2">
                    <button class="rounded-lg bg-blue-600 px-3 py-1 text-xs text-white hover:bg-blue-700">Edit</button>
                    <button class="rounded-lg bg-red-600 px-3 py-1 text-xs text-white hover:bg-red-700">Delete</button>
                </div>
            </div>
            <?php endforeach; ?>
            
            <?php if (empty($upcomingEvents)): ?>
            <div class="text-center py-8">
                <p class="text-gray-500 dark:text-gray-400">No upcoming events</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Calendar specific JavaScript
console.log('Calendar page loaded');

// Initialize calendar functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Calendar initialization code would go here
});
</script>
<?= $this->endSection() ?>
