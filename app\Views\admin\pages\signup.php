<?php /** @var string $title */ ?>
<?= $this->extend('layouts/auth') ?>

<?= $this->section('head') ?>
<!-- Sign up specific head content -->
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="text-center mb-8">
    <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Create Account</h2>
    <p class="mt-2 text-gray-600 dark:text-gray-400">Join us today! Please fill in your information</p>
</div>

<?php if (session()->getFlashdata('error')): ?>
<div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
    <p class="text-sm text-red-600"><?= esc(session()->getFlashdata('error')) ?></p>
</div>
<?php endif; ?>

<form action="/admin/signup" method="POST" class="space-y-6">
    <?= csrf_field() ?>
    
    <div class="grid grid-cols-2 gap-4">
        <div>
            <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                First Name
            </label>
            <input 
                type="text" 
                id="first_name" 
                name="first_name" 
                value="<?= old('first_name') ?>"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-blue-400 dark:focus:border-blue-400"
                placeholder="John"
            />
            <?php if (session()->getFlashdata('validation') && session()->getFlashdata('validation')->hasError('first_name')): ?>
                <p class="mt-1 text-sm text-red-600"><?= esc(session()->getFlashdata('validation')->getError('first_name')) ?></p>
            <?php endif; ?>
        </div>

        <div>
            <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Last Name
            </label>
            <input 
                type="text" 
                id="last_name" 
                name="last_name" 
                value="<?= old('last_name') ?>"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-blue-400 dark:focus:border-blue-400"
                placeholder="Doe"
            />
            <?php if (session()->getFlashdata('validation') && session()->getFlashdata('validation')->hasError('last_name')): ?>
                <p class="mt-1 text-sm text-red-600"><?= esc(session()->getFlashdata('validation')->getError('last_name')) ?></p>
            <?php endif; ?>
        </div>
    </div>

    <div>
        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Email Address
        </label>
        <div class="relative">
            <input 
                type="email" 
                id="email" 
                name="email" 
                value="<?= old('email') ?>"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-blue-400 dark:focus:border-blue-400"
                placeholder="<EMAIL>"
            />
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                </svg>
            </div>
        </div>
        <?php if (session()->getFlashdata('validation') && session()->getFlashdata('validation')->hasError('email')): ?>
            <p class="mt-1 text-sm text-red-600"><?= esc(session()->getFlashdata('validation')->getError('email')) ?></p>
        <?php endif; ?>
    </div>

    <div>
        <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Password
        </label>
        <div class="relative">
            <input 
                type="password" 
                id="password" 
                name="password" 
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-blue-400 dark:focus:border-blue-400"
                placeholder="Create a strong password"
            />
            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword('password')">
                <svg id="eye-open-1" class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <svg id="eye-closed-1" class="h-5 w-5 text-gray-400 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                </svg>
            </button>
        </div>
        <?php if (session()->getFlashdata('validation') && session()->getFlashdata('validation')->hasError('password')): ?>
            <p class="mt-1 text-sm text-red-600"><?= esc(session()->getFlashdata('validation')->getError('password')) ?></p>
        <?php endif; ?>
        <div class="mt-2">
            <div class="flex items-center space-x-2 text-xs">
                <div id="length-check" class="flex items-center">
                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span class="ml-1 text-gray-500">8+ characters</span>
                </div>
                <div id="uppercase-check" class="flex items-center">
                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span class="ml-1 text-gray-500">Uppercase</span>
                </div>
                <div id="number-check" class="flex items-center">
                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span class="ml-1 text-gray-500">Number</span>
                </div>
            </div>
        </div>
    </div>

    <div>
        <label for="password_confirm" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Confirm Password
        </label>
        <div class="relative">
            <input 
                type="password" 
                id="password_confirm" 
                name="password_confirm" 
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-blue-400 dark:focus:border-blue-400"
                placeholder="Confirm your password"
            />
            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword('password_confirm')">
                <svg id="eye-open-2" class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <svg id="eye-closed-2" class="h-5 w-5 text-gray-400 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                </svg>
            </button>
        </div>
        <?php if (session()->getFlashdata('validation') && session()->getFlashdata('validation')->hasError('password_confirm')): ?>
            <p class="mt-1 text-sm text-red-600"><?= esc(session()->getFlashdata('validation')->getError('password_confirm')) ?></p>
        <?php endif; ?>
    </div>

    <div class="flex items-center">
        <input 
            id="terms" 
            name="terms" 
            type="checkbox" 
            required
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600"
        />
        <label for="terms" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
            I agree to the 
            <a href="#" class="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">Terms of Service</a> 
            and 
            <a href="#" class="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">Privacy Policy</a>
        </label>
    </div>

    <div>
        <button 
            type="submit" 
            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition duration-150 ease-in-out"
        >
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white hidden" id="loading-spinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span id="button-text">Create Account</span>
        </button>
    </div>
</form>

<p class="mt-8 text-center text-sm text-gray-600 dark:text-gray-400">
    Already have an account?
    <a href="/admin/signin" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
        Sign in here
    </a>
</p>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const eyeOpen = document.getElementById(fieldId === 'password' ? 'eye-open-1' : 'eye-open-2');
    const eyeClosed = document.getElementById(fieldId === 'password' ? 'eye-closed-1' : 'eye-closed-2');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeOpen.classList.add('hidden');
        eyeClosed.classList.remove('hidden');
    } else {
        passwordInput.type = 'password';
        eyeOpen.classList.remove('hidden');
        eyeClosed.classList.add('hidden');
    }
}

// Password strength validation
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const lengthCheck = document.getElementById('length-check');
    const uppercaseCheck = document.getElementById('uppercase-check');
    const numberCheck = document.getElementById('number-check');
    
    // Length check
    if (password.length >= 8) {
        lengthCheck.querySelector('svg').classList.remove('text-gray-400');
        lengthCheck.querySelector('svg').classList.add('text-green-500');
        lengthCheck.querySelector('span').classList.remove('text-gray-500');
        lengthCheck.querySelector('span').classList.add('text-green-600');
    } else {
        lengthCheck.querySelector('svg').classList.add('text-gray-400');
        lengthCheck.querySelector('svg').classList.remove('text-green-500');
        lengthCheck.querySelector('span').classList.add('text-gray-500');
        lengthCheck.querySelector('span').classList.remove('text-green-600');
    }
    
    // Uppercase check
    if (/[A-Z]/.test(password)) {
        uppercaseCheck.querySelector('svg').classList.remove('text-gray-400');
        uppercaseCheck.querySelector('svg').classList.add('text-green-500');
        uppercaseCheck.querySelector('span').classList.remove('text-gray-500');
        uppercaseCheck.querySelector('span').classList.add('text-green-600');
    } else {
        uppercaseCheck.querySelector('svg').classList.add('text-gray-400');
        uppercaseCheck.querySelector('svg').classList.remove('text-green-500');
        uppercaseCheck.querySelector('span').classList.add('text-gray-500');
        uppercaseCheck.querySelector('span').classList.remove('text-green-600');
    }
    
    // Number check
    if (/[0-9]/.test(password)) {
        numberCheck.querySelector('svg').classList.remove('text-gray-400');
        numberCheck.querySelector('svg').classList.add('text-green-500');
        numberCheck.querySelector('span').classList.remove('text-gray-500');
        numberCheck.querySelector('span').classList.add('text-green-600');
    } else {
        numberCheck.querySelector('svg').classList.add('text-gray-400');
        numberCheck.querySelector('svg').classList.remove('text-green-500');
        numberCheck.querySelector('span').classList.add('text-gray-500');
        numberCheck.querySelector('span').classList.remove('text-green-600');
    }
});

// Password confirmation validation
document.getElementById('password_confirm').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (confirmPassword && password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

// Form submission with loading state
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('password_confirm').value;

    if (password !== confirmPassword) {
        e.preventDefault();
        alert('Passwords do not match');
        return false;
    }

    const button = this.querySelector('button[type="submit"]');
    const spinner = document.getElementById('loading-spinner');
    const buttonText = document.getElementById('button-text');

    button.disabled = true;
    spinner.classList.remove('hidden');
    buttonText.textContent = 'Creating Account...';
});
</script>
<?= $this->endSection() ?>
