<?php /** @var string $title */ /** @var string|null $sidebar */ /** @var string|null $header */ ?>
<?= $this->extend('layouts/admin') ?>

<?= $this->section('head') ?>
<!-- Alpine.js test page specific head content -->
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="mx-auto max-w-(--breakpoint-2xl) p-4 md:p-6">
    <!-- Breadcrumb -->
    <div class="mb-6 flex flex-wrap items-center justify-between gap-3">
        <div>
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="/admin" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">Alpine.js Test</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Alpine.js Test -->
    <div class="grid grid-cols-12 gap-4 md:gap-6">
        <div class="col-span-12">
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
                <h3 class="mb-6 text-title-md font-bold text-gray-800 dark:text-white/90">Alpine.js Functionality Test</h3>
                
                <!-- Simple Counter Test -->
                <div class="mb-8" x-data="{ count: 0 }">
                    <h4 class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">Simple Counter Test</h4>
                    <div class="flex items-center space-x-4">
                        <button 
                            @click="count--"
                            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                        >
                            Decrease
                        </button>
                        <span 
                            x-text="count"
                            class="text-2xl font-bold text-gray-800 dark:text-white min-w-[3rem] text-center"
                        ></span>
                        <button 
                            @click="count++"
                            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                        >
                            Increase
                        </button>
                    </div>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        If Alpine.js is working, clicking the buttons should update the counter.
                    </p>
                </div>

                <!-- Toggle Test -->
                <div class="mb-8" x-data="{ isVisible: false }">
                    <h4 class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">Toggle Visibility Test</h4>
                    <button 
                        @click="isVisible = !isVisible"
                        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mb-4"
                    >
                        <span x-text="isVisible ? 'Hide' : 'Show'"></span> Content
                    </button>
                    <div 
                        x-show="isVisible"
                        x-transition
                        class="p-4 bg-green-100 border border-green-300 rounded dark:bg-green-900 dark:border-green-700"
                    >
                        <p class="text-green-800 dark:text-green-200">
                            🎉 Alpine.js is working! This content is toggled with x-show and x-transition.
                        </p>
                    </div>
                </div>

                <!-- Form Input Test -->
                <div class="mb-8" x-data="{ message: '' }">
                    <h4 class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">Form Input Test</h4>
                    <input 
                        x-model="message"
                        type="text" 
                        placeholder="Type something..."
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white mb-4"
                    />
                    <div class="p-4 bg-gray-100 rounded dark:bg-gray-700">
                        <p class="text-gray-700 dark:text-gray-300">
                            You typed: <span x-text="message || 'Nothing yet...'" class="font-semibold"></span>
                        </p>
                    </div>
                </div>

                <!-- Dropdown Test -->
                <div class="mb-8" x-data="{ open: false }" @click.outside="open = false">
                    <h4 class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">Dropdown Test</h4>
                    <div class="relative">
                        <button 
                            @click="open = !open"
                            class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 flex items-center"
                        >
                            <span>Dropdown Menu</span>
                            <svg 
                                :class="open ? 'rotate-180' : ''"
                                class="ml-2 w-4 h-4 transition-transform"
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div 
                            x-show="open"
                            x-transition
                            class="absolute top-full left-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10 dark:bg-gray-800 dark:border-gray-700"
                        >
                            <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">Option 1</a>
                            <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">Option 2</a>
                            <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">Option 3</a>
                        </div>
                    </div>
                </div>

                <!-- Status Display -->
                <div class="border-t pt-6">
                    <h4 class="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">Alpine.js Status</h4>
                    <div id="alpine-status" class="p-4 bg-yellow-100 border border-yellow-300 rounded dark:bg-yellow-900 dark:border-yellow-700">
                        <p class="text-yellow-800 dark:text-yellow-200">
                            ⏳ Checking Alpine.js status...
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusDiv = document.getElementById('alpine-status');
    
    // Check if Alpine.js is loaded
    setTimeout(function() {
        if (typeof Alpine !== 'undefined') {
            statusDiv.innerHTML = `
                <p class="text-green-800 dark:text-green-200">
                    ✅ Alpine.js is loaded and working properly!
                </p>
            `;
            statusDiv.className = 'p-4 bg-green-100 border border-green-300 rounded dark:bg-green-900 dark:border-green-700';
        } else {
            statusDiv.innerHTML = `
                <p class="text-red-800 dark:text-red-200">
                    ❌ Alpine.js is not loaded. Check if bundle.js is loading correctly.
                </p>
            `;
            statusDiv.className = 'p-4 bg-red-100 border border-red-300 rounded dark:bg-red-900 dark:border-red-700';
        }
    }, 1000);
});
</script>
<?= $this->endSection() ?>
