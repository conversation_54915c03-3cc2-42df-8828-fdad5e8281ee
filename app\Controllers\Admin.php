<?php

namespace App\Controllers;

use CodeIgniter\HTTP\ResponseInterface;

class Admin extends BaseController
{
    private string $buildPath;

    public function __construct()
    {
        // Absolute path to TailAdmin build folder
        $this->buildPath = realpath(ROOTPATH . 'tailadmin/build');
    }

    public function index(): ResponseInterface
    {
        return $this->renderPage('index.html', 'index');
    }

    public function page(string $slug = 'index'): ResponseInterface
    {
        // Whitelist of allowed pages (from build output)
        $allowed = [
            'index','calendar','profile','form-elements','basic-tables','images','videos','buttons',
            'alerts','avatars','badge','bar-chart','blank','line-chart','sidebar','signin','signup','404',
            'forgot-password','reset-password','js-test','alpine-test','debug-content'
        ];

        if (! in_array($slug, $allowed, true)) {
            return $this->response->setStatusCode(404)->setBody('Page not found');
        }

        $filename = $slug . '.html';
        return $this->renderPage($filename, $slug);
    }

    public function asset(string $path = ''): ResponseInterface
    {
        // Prevent directory traversal
        $clean = str_replace(['..', "\\", '\0'], '', $path);
        $fullPath = $this->buildPath . DIRECTORY_SEPARATOR . $clean;

        if (! $this->buildPath || ! is_file($fullPath)) {
            return $this->response->setStatusCode(404)->setBody('Asset not found');
        }

        $mime = $this->detectMime($fullPath);
        return $this->response
            ->setContentType($mime)
            ->setBody(file_get_contents($fullPath));
    }

    private function renderPage(string $file, ?string $slug = null): ResponseInterface
    {
        if (! $this->buildPath) {
            return $this->response->setStatusCode(500)->setBody('TailAdmin build path not found');
        }

        $full = $this->buildPath . DIRECTORY_SEPARATOR . $file;
        if (! is_file($full)) {
            return $this->response->setStatusCode(404)->setBody('Page not found');
        }

        $html = file_get_contents($full);

        // Fix asset URLs to point to /admin/assets/
        $html = $this->rewriteAssets($html);
        // Fix internal links to point to /admin/{page}
        $html = $this->rewriteInternalLinks($html);

        // Extract <title>
        $title = null;
        if (preg_match('/<title>(.*?)<\/title>/si', $html, $m)) {
            $title = trim($m[1]);
        }

        // Extract sidebar and header for full TailAdmin UI
        [$sidebar, $header] = $this->extractChrome($html);

        // Extract main content area only (usually inside a main tag or specific div)
        $bodyContent = '';

        // First try to extract content from <main> tag
        if (preg_match('#<main[^>]*>([\s\S]*?)</main>#i', $html, $m)) {
            $bodyContent = $m[1];
        }
        // If no main tag, try to extract from common content containers
        elseif (preg_match('#<div[^>]*class="[^"]*content[^"]*"[^>]*>([\s\S]*?)</div>#i', $html, $m)) {
            $bodyContent = $m[1];
        }
        // If no specific content container, extract body but clean it up
        elseif (preg_match('/<body[^>]*>([\s\S]*)<\/body>/i', $html, $m)) {
            $bodyContent = $m[1];
            // Remove sidebar and header from body content
            $bodyContent = preg_replace('#<aside[\s\S]*?</aside>#i', '', $bodyContent);
            $bodyContent = preg_replace('#<header[\s\S]*?</header>#i', '', $bodyContent);
            // Remove script tags to avoid conflicts
            $bodyContent = preg_replace('#<script[\s\S]*?</script>#i', '', $bodyContent);
            // Remove any Alpine.js attributes that might be floating around
            $bodyContent = preg_replace('#x-data="[^"]*"#i', '', $bodyContent);
            $bodyContent = preg_replace('#x-init="[^"]*"#i', '', $bodyContent);
            $bodyContent = preg_replace('#:class="[^"]*"#i', '', $bodyContent);
        }

        // Clean up any remaining Alpine.js artifacts
        $bodyContent = trim($bodyContent);

        // Check if we have a dedicated CI view for this page
        $dedicatedPages = ['index', 'calendar', 'profile', 'form-elements', 'basic-tables', 'js-test', 'alpine-test', 'debug-content'];
        $authPages = ['signin', 'signup', 'forgot-password', 'reset-password'];

        if ($slug && in_array($slug, $authPages, true)) {
            // Use auth layout for authentication pages
            $html = view("admin/pages/{$slug}", compact('title'));
        } elseif ($slug && in_array($slug, $dedicatedPages, true)) {
            // Use admin layout for dedicated pages
            $html = view("admin/pages/{$slug}", compact('title', 'sidebar', 'header'));
        } else {
            // Use dynamic content injection for other pages
            $content = $bodyContent;
            $title   = $title ?? 'Admin';
            $html = view('admin/section_wrapper', compact('title', 'content', 'sidebar', 'header'));
        }

        return $this->response->setBody($html);
    }

    private function rewriteAssets(string $html): string
    {
        // style.css, bundle.js, favicon.ico to /admin/assets/*
        $replacements = [
            'href="style.css"' => 'href="/admin/assets/style.css"',
            'src="bundle.js"'  => 'src="/admin/assets/bundle.js"',
            'href="favicon.ico"' => 'href="/admin/assets/favicon.ico"',
        ];

        $html = strtr($html, $replacements);

        // src/images/... and src/... to /admin/assets/src/...
        $html = preg_replace('#(src|href)="src/([^"\>]+)"#i', '$1="/admin/assets/src/$2"', $html);

        return $html;
    }

    private function rewriteInternalLinks(string $html): string
    {
        // Map known page links like calendar.html -> /admin/calendar
        $map = [
            'index' => 'index',
            'calendar' => 'calendar',
            'profile' => 'profile',
            'form-elements' => 'form-elements',
            'basic-tables' => 'basic-tables',
            'images' => 'images',
            'videos' => 'videos',
            'buttons' => 'buttons',
            'alerts' => 'alerts',
            'avatars' => 'avatars',
            'badge' => 'badge',
            'bar-chart' => 'bar-chart',
            'blank' => 'blank',
            'line-chart' => 'line-chart',
            'sidebar' => 'sidebar',
            'signin' => 'signin',
            'signup' => 'signup',
            '404' => '404',
        ];

        foreach ($map as $file => $slug) {
            $html = preg_replace('#href=\"' . preg_quote($file, '#') . '\.html\"#i', 'href="/admin/' . $slug . '"', $html);
        }

        return $html;
    }

    private function detectMime(string $path): string
    {
        $ext = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        return match ($ext) {
            'css' => 'text/css',
            'js' => 'application/javascript',
            'svg' => 'image/svg+xml',
            'png' => 'image/png',
            'jpg', 'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'ico' => 'image/x-icon',
            'json' => 'application/json',
            default => 'application/octet-stream',
        };
    }

    /**
     * Extracts the full <aside> sidebar and <header> header from a built HTML file
     * so we can inject them into the CI layout for visual parity with TailAdmin.
     */
    private function extractChrome(string $html): array
    {
        $sidebar = null;
        $header  = null;

        if (preg_match('#(<aside[\s\S]*?</aside>)#i', $html, $m)) {
            $sidebar = $m[1];
        }
        if (preg_match('#(<header[\s\S]*?</header>)#i', $html, $m)) {
            $header = $m[1];
        }
        // Rewrite their asset and internal links too
        if ($sidebar) $sidebar = $this->rewriteAssets($this->rewriteInternalLinks($sidebar));
        if ($header)  $header  = $this->rewriteAssets($this->rewriteInternalLinks($header));
        return [$sidebar, $header];
    }
}


