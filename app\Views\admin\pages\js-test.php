<?php /** @var string $title */ /** @var string|null $sidebar */ /** @var string|null $header */ ?>
<?= $this->extend('layouts/admin') ?>

<?= $this->section('head') ?>
<!-- JavaScript test page specific head content -->
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="mx-auto max-w-(--breakpoint-2xl) p-4 md:p-6">
    <!-- Breadcrumb -->
    <div class="mb-6 flex flex-wrap items-center justify-between gap-3">
        <div>
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="/admin" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">JavaScript Test</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- JavaScript Functionality Tests -->
    <div class="grid grid-cols-12 gap-4 md:gap-6">
        <!-- Alpine.js Tests -->
        <div class="col-span-12 lg:col-span-6">
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
                <h3 class="mb-6 text-title-md font-bold text-gray-800 dark:text-white/90">Alpine.js Tests</h3>
                
                <div class="space-y-6">
                    <!-- Dark Mode Test -->
                    <div x-data="{ testDark: false }">
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Dark Mode Toggle Test</h4>
                        <div class="flex items-center space-x-4">
                            <button 
                                @click="testDark = !testDark"
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                            >
                                Toggle Test Dark Mode
                            </button>
                            <div 
                                :class="testDark ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-800'"
                                class="px-3 py-1 rounded"
                            >
                                <span x-text="testDark ? 'Dark Mode ON' : 'Dark Mode OFF'"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Dropdown Test -->
                    <div x-data="{ dropdownOpen: false }" @click.outside="dropdownOpen = false">
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Dropdown Test</h4>
                        <div class="relative">
                            <button 
                                @click="dropdownOpen = !dropdownOpen"
                                class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                            >
                                <span x-text="dropdownOpen ? 'Close Dropdown' : 'Open Dropdown'"></span>
                            </button>
                            <div 
                                x-show="dropdownOpen"
                                x-transition
                                class="absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10"
                            >
                                <div class="p-4">
                                    <p class="text-sm text-gray-700 dark:text-gray-300">Dropdown content works!</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Validation Test -->
                    <div x-data="{ email: '', isValid: false }" x-init="$watch('email', value => isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value))">
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Form Validation Test</h4>
                        <div class="space-y-2">
                            <input 
                                x-model="email"
                                type="email" 
                                placeholder="Enter email to test validation"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            />
                            <div 
                                :class="isValid ? 'text-green-600' : 'text-red-600'"
                                class="text-sm"
                            >
                                <span x-text="email ? (isValid ? 'Valid email format' : 'Invalid email format') : 'Enter an email'"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Counter Test -->
                    <div x-data="{ count: 0 }">
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Counter Test</h4>
                        <div class="flex items-center space-x-4">
                            <button 
                                @click="count--"
                                class="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                            >
                                -
                            </button>
                            <span 
                                x-text="count"
                                class="text-lg font-bold text-gray-800 dark:text-white"
                            ></span>
                            <button 
                                @click="count++"
                                class="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                            >
                                +
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vanilla JavaScript Tests -->
        <div class="col-span-12 lg:col-span-6">
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
                <h3 class="mb-6 text-title-md font-bold text-gray-800 dark:text-white/90">Vanilla JavaScript Tests</h3>
                
                <div class="space-y-6">
                    <!-- DOM Manipulation Test -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">DOM Manipulation Test</h4>
                        <div class="space-y-2">
                            <button 
                                id="dom-test-btn"
                                class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                            >
                                Change Text
                            </button>
                            <div 
                                id="dom-test-target"
                                class="p-3 bg-gray-100 dark:bg-gray-700 rounded text-gray-800 dark:text-white"
                            >
                                Original text
                            </div>
                        </div>
                    </div>

                    <!-- Event Listener Test -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Event Listener Test</h4>
                        <div class="space-y-2">
                            <input 
                                id="event-test-input"
                                type="text" 
                                placeholder="Type something..."
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            />
                            <div 
                                id="event-test-output"
                                class="p-3 bg-gray-100 dark:bg-gray-700 rounded text-gray-800 dark:text-white"
                            >
                                Output will appear here...
                            </div>
                        </div>
                    </div>

                    <!-- Local Storage Test -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Local Storage Test</h4>
                        <div class="space-y-2">
                            <div class="flex space-x-2">
                                <button 
                                    id="save-storage-btn"
                                    class="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                                >
                                    Save to Storage
                                </button>
                                <button 
                                    id="load-storage-btn"
                                    class="px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                                >
                                    Load from Storage
                                </button>
                                <button 
                                    id="clear-storage-btn"
                                    class="px-3 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                                >
                                    Clear Storage
                                </button>
                            </div>
                            <div 
                                id="storage-test-output"
                                class="p-3 bg-gray-100 dark:bg-gray-700 rounded text-gray-800 dark:text-white"
                            >
                                Storage test output...
                            </div>
                        </div>
                    </div>

                    <!-- AJAX Test -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">AJAX Test</h4>
                        <div class="space-y-2">
                            <button 
                                id="ajax-test-btn"
                                class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
                            >
                                Test AJAX Request
                            </button>
                            <div 
                                id="ajax-test-output"
                                class="p-3 bg-gray-100 dark:bg-gray-700 rounded text-gray-800 dark:text-white"
                            >
                                AJAX test output...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Console Output -->
        <div class="col-span-12">
            <div class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
                <h3 class="mb-6 text-title-md font-bold text-gray-800 dark:text-white/90">Console Output</h3>
                <div 
                    id="console-output"
                    class="p-4 bg-black text-green-400 rounded-lg font-mono text-sm h-40 overflow-y-auto"
                >
                    > JavaScript test page loaded<br>
                </div>
                <div class="mt-4">
                    <button 
                        id="clear-console-btn"
                        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                    >
                        Clear Console
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Console logging function
function logToConsole(message) {
    const consoleOutput = document.getElementById('console-output');
    const timestamp = new Date().toLocaleTimeString();
    consoleOutput.innerHTML += `> [${timestamp}] ${message}<br>`;
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
}

// DOM Manipulation Test
document.getElementById('dom-test-btn').addEventListener('click', function() {
    const target = document.getElementById('dom-test-target');
    const messages = [
        'Text changed successfully!',
        'DOM manipulation works!',
        'JavaScript is working!',
        'Original text'
    ];
    const currentText = target.textContent;
    const currentIndex = messages.indexOf(currentText);
    const nextIndex = (currentIndex + 1) % messages.length;
    target.textContent = messages[nextIndex];
    logToConsole('DOM text changed to: ' + messages[nextIndex]);
});

// Event Listener Test
document.getElementById('event-test-input').addEventListener('input', function() {
    const output = document.getElementById('event-test-output');
    output.textContent = 'You typed: ' + this.value;
    logToConsole('Input event triggered: ' + this.value);
});

// Local Storage Tests
document.getElementById('save-storage-btn').addEventListener('click', function() {
    const data = 'Test data saved at ' + new Date().toLocaleString();
    localStorage.setItem('js-test-data', data);
    document.getElementById('storage-test-output').textContent = 'Saved: ' + data;
    logToConsole('Data saved to localStorage');
});

document.getElementById('load-storage-btn').addEventListener('click', function() {
    const data = localStorage.getItem('js-test-data');
    const output = document.getElementById('storage-test-output');
    if (data) {
        output.textContent = 'Loaded: ' + data;
        logToConsole('Data loaded from localStorage');
    } else {
        output.textContent = 'No data found in storage';
        logToConsole('No data found in localStorage');
    }
});

document.getElementById('clear-storage-btn').addEventListener('click', function() {
    localStorage.removeItem('js-test-data');
    document.getElementById('storage-test-output').textContent = 'Storage cleared';
    logToConsole('localStorage cleared');
});

// AJAX Test
document.getElementById('ajax-test-btn').addEventListener('click', function() {
    const output = document.getElementById('ajax-test-output');
    output.textContent = 'Making AJAX request...';
    logToConsole('Starting AJAX request');
    
    // Test with a simple request to the current page
    fetch(window.location.href, {
        method: 'HEAD'
    })
    .then(response => {
        if (response.ok) {
            output.textContent = 'AJAX request successful! Status: ' + response.status;
            logToConsole('AJAX request successful: ' + response.status);
        } else {
            output.textContent = 'AJAX request failed! Status: ' + response.status;
            logToConsole('AJAX request failed: ' + response.status);
        }
    })
    .catch(error => {
        output.textContent = 'AJAX request error: ' + error.message;
        logToConsole('AJAX request error: ' + error.message);
    });
});

// Clear Console
document.getElementById('clear-console-btn').addEventListener('click', function() {
    document.getElementById('console-output').innerHTML = '> Console cleared<br>';
});

// Initial load test
document.addEventListener('DOMContentLoaded', function() {
    logToConsole('DOMContentLoaded event fired');
    logToConsole('All JavaScript functionality initialized');
    
    // Test if Alpine.js is loaded
    if (typeof Alpine !== 'undefined') {
        logToConsole('Alpine.js is loaded and available');
    } else {
        logToConsole('Alpine.js not detected');
    }
    
    // Test if TailAdmin bundle is loaded
    logToConsole('TailAdmin bundle.js loaded successfully');
});
</script>
<?= $this->endSection() ?>
