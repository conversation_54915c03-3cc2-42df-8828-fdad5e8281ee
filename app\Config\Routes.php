<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Admin (TailAdmin) routes
$routes->group('admin', static function (RouteCollection $routes) {
    // pages
    $routes->get('/', 'Admin::index');

    // Authentication routes (POST methods for form handling)
    $routes->match(['get', 'post'], 'signin', 'Admin::page/signin');
    $routes->match(['get', 'post'], 'signup', 'Admin::page/signup');
    $routes->match(['get', 'post'], 'forgot-password', 'Admin::page/forgot-password');
    $routes->match(['get', 'post'], 'reset-password', 'Admin::page/reset-password');

    // generic page route: /admin/{slug}
    $routes->get('(:segment)', 'Admin::page/$1');

    // assets under build folder
    $routes->get('assets', 'Admin::asset');
    $routes->get('assets/(:any)', 'Admin::asset/$1');
});
