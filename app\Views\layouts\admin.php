<?php /** @var string $title */ ?>
<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><?= esc($title ?? 'Admin') ?></title>
    <link rel="icon" href="/admin/assets/favicon.ico">
    <link href="/admin/assets/style.css" rel="stylesheet">
    <?= $this->renderSection('head') ?>
</head>
<body
    x-data="{ page: 'ci', 'loaded': true, 'darkMode': false, 'stickyMenu': false, 'sidebarToggle': false, 'scrollTop': false }"
    x-init="darkMode = JSON.parse(localStorage.getItem('darkMode')) || false; $watch('darkMode', value => localStorage.setItem('darkMode', JSON.stringify(value)))"
    :class="{'dark bg-gray-900': darkMode === true}"
>
<!-- Page Wrapper Start -->
<div class="flex h-screen overflow-hidden">
    <!-- Sidebar Start -->
    <?php if (isset($sidebar) && $sidebar): ?>
        <?php
        // Output extracted sidebar without escaping
        echo $sidebar;
        ?>
    <?php else: ?>
        <?= $this->include('partials/sidebar') ?>
    <?php endif; ?>
    <!-- Sidebar End -->

    <!-- Content Area Start -->
    <div class="relative flex flex-col flex-1 overflow-x-hidden overflow-y-auto">
        <!-- Header Start -->
        <?php if (isset($header) && $header): ?>
            <?php
            // Output extracted header without escaping
            echo $header;
            ?>
        <?php else: ?>
            <?= $this->include('partials/header') ?>
        <?php endif; ?>
        <!-- Header End -->

        <!-- Main Content Start -->
        <main>
            <?= $this->renderSection('content') ?>
        </main>
        <!-- Main Content End -->
    </div>
    <!-- Content Area End -->
</div>
<!-- Page Wrapper End -->

<script defer src="/admin/assets/bundle.js"></script>
<script>
// Debug script to check Alpine.js loading
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded');
    setTimeout(function() {
        if (typeof Alpine !== 'undefined') {
            console.log('Alpine.js loaded successfully');
        } else {
            console.error('Alpine.js not loaded');
        }
    }, 100);
});
</script>
<?= $this->renderSection('scripts') ?>
</body>
</html>

