<?php /** @var string $title */ ?>
<?= $this->extend('layouts/auth') ?>

<?= $this->section('head') ?>
<!-- Forgot password specific head content -->
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="text-center mb-8">
    <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Forgot Password?</h2>
    <p class="mt-2 text-gray-600 dark:text-gray-400">No worries! Enter your email and we'll send you reset instructions</p>
</div>

<?php if (session()->getFlashdata('error')): ?>
<div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm text-red-600"><?= esc(session()->getFlashdata('error')) ?></p>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (session()->getFlashdata('success')): ?>
<div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm text-green-600"><?= esc(session()->getFlashdata('success')) ?></p>
        </div>
    </div>
</div>
<?php endif; ?>

<form action="/admin/forgot-password" method="POST" class="space-y-6">
    <?= csrf_field() ?>
    
    <div>
        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Email Address
        </label>
        <div class="relative">
            <input 
                type="email" 
                id="email" 
                name="email" 
                value="<?= old('email') ?>"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-blue-400 dark:focus:border-blue-400"
                placeholder="Enter your email address"
            />
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                </svg>
            </div>
        </div>
        <?php if (session()->getFlashdata('validation') && session()->getFlashdata('validation')->hasError('email')): ?>
            <p class="mt-1 text-sm text-red-600"><?= esc(session()->getFlashdata('validation')->getError('email')) ?></p>
        <?php endif; ?>
    </div>

    <div>
        <button 
            type="submit" 
            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition duration-150 ease-in-out"
        >
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white hidden" id="loading-spinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span id="button-text">Send Reset Instructions</span>
        </button>
    </div>
</form>

<div class="mt-8 text-center">
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <div class="flex items-center justify-center mb-2">
            <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">What happens next?</h3>
        <p class="text-sm text-blue-700 dark:text-blue-300">
            We'll send you an email with a secure link to reset your password. The link will expire in 1 hour for security reasons.
        </p>
    </div>
</div>

<div class="mt-8 text-center space-y-4">
    <p class="text-sm text-gray-600 dark:text-gray-400">
        Remember your password?
        <a href="/admin/signin" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
            Back to Sign In
        </a>
    </p>
    
    <div class="relative">
        <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
        </div>
        <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">Or</span>
        </div>
    </div>
    
    <p class="text-sm text-gray-600 dark:text-gray-400">
        Don't have an account?
        <a href="/admin/signup" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
            Create one here
        </a>
    </p>
</div>

<!-- Help Section -->
<div class="mt-8 border-t border-gray-200 dark:border-gray-700 pt-6">
    <div class="text-center">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Need help?</h4>
        <div class="flex justify-center space-x-6 text-sm">
            <a href="#" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                FAQ
            </a>
            <a href="#" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Contact Support
            </a>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Form submission with loading state
document.querySelector('form').addEventListener('submit', function() {
    const button = this.querySelector('button[type="submit"]');
    const spinner = document.getElementById('loading-spinner');
    const buttonText = document.getElementById('button-text');
    
    button.disabled = true;
    spinner.classList.remove('hidden');
    buttonText.textContent = 'Sending...';
});

// Auto-focus email input
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('email').focus();
});
</script>
<?= $this->endSection() ?>
